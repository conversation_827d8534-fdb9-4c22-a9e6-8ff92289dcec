
import React from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import MobileNav from "./MobileNav";
import { AuthButton } from "@/components/auth/AuthButton";


interface MenuItem {
  title: string;
  href: string;
}

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Updated menu items
  const menuItems: MenuItem[] = [
    { title: "Home", href: "/" },
    { title: "Software", href: "/software" },
    { title: "Hardware", href: "/hardware" },
    { title: "Resellers", href: "/resellers" },
    { title: "Pricing", href: "/pricing" },
    { title: "What's New", href: "/whats-new" },
    { title: "Contact", href: "/contact" },
  ];



  // Modified to handle section navigation and page navigation properly
  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      // Always try to scroll to section on current page first
      const element = document.getElementById(href.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return;
      }

      // If we're not on the home page and section wasn't found, navigate home with scrollTo
      if (location.pathname !== '/') {
        navigate('/', { state: { scrollTo: href.substring(1) } });
      }
    } else {
      // For non-hash links, use regular navigation
      navigate(href);
    }
  };

  return (
    <motion.header
      className="fixed w-full z-50 top-0 bg-gradient-to-r from-gray-900/95 via-gray-800/90 to-gray-900/95 backdrop-blur-xl border-b border-purple-500/20 shadow-lg shadow-purple-900/10 transition-all duration-500"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      {/* Enhanced glass morphism overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#C084FC]/5 via-transparent to-[#C084FC]/5 backdrop-blur-xl"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="flex items-center justify-between py-4">
          {/* Logo - Enhanced Nexus Style */}
          <Link to="/" className="flex items-center space-x-3 group">
            {/* SVG Logo */}
            <motion.div
              className="relative drop-shadow-lg"
              whileHover={{
                scale: 1.08,
                filter: "drop-shadow(0 0 20px rgba(192, 132, 252, 0.4))"
              }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <svg
                viewBox="0 0 930 465"
                className="w-36 h-18 transition-all duration-300 group-hover:drop-shadow-2xl"
              >
                {/* Enhanced glow filter */}
                <defs>
                  <filter id="navbar-glow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="4" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>
                  <filter id="navbar-glow-hover" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="6" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>
                </defs>

                {/* Pegasus Tool Text Path */}
                <motion.path
                  d="m360.6 212.1h-25.7v20.9h-20.1v-20.8l19.2-17.7h23.5q4.1 0 6.5-2.4 2.6-2.5 2.6-6.6 0-4.1-2.6-6.3-2.4-2.2-6.5-2.2h-28l-14.7-17.6h45.8q5.9 0 10.7 1.9 4.8 1.9 8.2 5.4 3.5 3.5 5.3 8.3 1.9 4.8 1.9 10.7 0 6-1.9 10.9-1.8 4.9-5.3 8.3-3.4 3.4-8.2 5.3-4.8 1.9-10.7 1.9zm35.6-52.7h62.3v17.6h-42.1v9.3h42.1l-18.8 17.6h-23.2v11.6h42v17.5h-62.3zm110.9 75.7q-8.8 0-16.3-2.8-7.5-2.8-13.1-8-5.5-5.1-8.7-12.3-3.1-7.1-3.1-15.9 0-8.7 3.1-15.9 3.2-7.1 8.7-12.2 5.6-5.1 13.2-7.9 7.5-2.8 16.3-2.8 8.5 0 16.1 2.6 7.8 2.7 13 7.6l-14.1 12.8q-2.5-2.6-6-4-3.5-1.5-7.9-1.5-4.5 0-8.5 1.6-4 1.5-7 4.3-3.1 2.9-4.9 6.9-1.7 3.9-1.7 8.6 0 4.8 1.7 8.8 1.8 3.9 4.9 6.7 3 2.8 7 4.3 4.1 1.5 8.6 1.5 1.2 0 2.8-0.1 1.5-0.1 3.1-0.3 1.6-0.2 3-0.4 1.4-0.3 2.4-0.8v-8.6l-15.4-17.6h35.7v43.3h-14.4q-1.6 0.5-4 0.9-2.5 0.3-5.1 0.6-2.5 0.2-5 0.5-2.5 0.1-4.4 0.1zm42.3-2.1l10.7-38.5h31.1l-9.8-35.1h20.9l20.7 73.6h-21l-5.9-21h-19.9l-5.9 21zm116-45.9q8.2 0 13.9 2 5.7 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.7 4.1 1.7 8.6 0 4.5-1.8 8.9-1.7 4.4-5.6 8-3.9 3.5-10.2 5.8-6.3 2.2-15.3 2.2-4.4 0-8.5-0.9-4.1-0.9-7.6-2.4-3.5-1.5-6.3-3.3-2.7-1.7-4.7-3.6l14.1-12.9q2.1 2.5 5.4 4 3.4 1.5 8.3 1.5 5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.5-2-11.2-2-8.3 0-14-1.9-5.7-2-9.2-5.1-3.5-3.3-5.1-7.4-1.6-4.2-1.6-8.5 0-4.5 1.7-8.9 1.8-4.5 5.7-8.1 3.9-3.6 10.2-5.8 6.3-2.2 15.3-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.4 3.5 1.4 6.2 3.2 2.8 1.8 4.8 3.7l-14 12.8q-2.1-2.4-5.5-3.9-3.4-1.6-8.3-1.6-5.7 0-8.8 1.9-3.1 1.8-3.1 4 0 2.4 3.6 4.4 3.6 2 11.2 2zm91.5-8.9l20.1-18.8v40.2q0 8.9-3.1 15.5-3.1 6.6-8.2 11.1-5.1 4.4-11.6 6.7-6.4 2.2-13.2 2.2-6.7 0-13.3-2.2-6.5-2.3-11.6-6.7-5.1-4.5-8.2-11.1-3.1-6.6-3.1-15.5v-40.2h20.2v38.9q0 4.8 1.4 8.4 1.3 3.6 3.6 6 2.3 2.4 5.1 3.7 2.9 1.1 5.9 1.1 3 0 5.9-1.1 2.8-1.3 5-3.7 2.3-2.4 3.7-6 1.4-3.6 1.4-8.4zm64.7 8.9q8.2 0 13.9 2 5.6 1.9 9.1 5.2 3.6 3.2 5.2 7.3 1.6 4.1 1.6 8.5 0 4.5-1.8 9-1.6 4.4-5.5 7.9-3.9 3.6-10.2 5.8-6.3 2.2-15.4 2.2-4.4 0-8.5-0.8-4.1-1-7.5-2.4-3.5-1.5-6.3-3.3-2.8-1.8-4.8-3.7l14.1-12.8q2.1 2.4 5.5 4 3.3 1.5 8.3 1.5q5.7 0 8.7-1.8 3.2-1.9 3.2-4.1 0-2.4-3.6-4.4-3.6-2-11.3-2-8.3 0-13.9-1.9-5.7-2-9.3-5.2-3.5-3.2-5-7.3-1.6-4.2-1.6-8.5 0-4.5 1.7-9 1.8-4.5 5.6-8 3.9-3.6 10.2-5.8 6.3-2.2 15.4-2.2 4.4 0 8.5 0.9 4.1 0.9 7.6 2.3 3.4 1.5 6.2 3.3 2.8 1.8 4.8 3.7l-14.1 12.8q-2.1-2.4-5.4-3.9-3.4-1.6-8.4-1.6-5.6 0-8.8 1.9-3 1.8-3 4 0 2.4 3.5 4.4 3.6 2 11.3 2zm-508.9 80.8v-17.5h72.3v17.5h-25.6v37.4l-20.1 18.6v-56zm76.2 19.1q0-8.7 2.8-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.8 0 16 2.8 7.1 2.7 12.3 7.9 5.1 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.8 7.2-7.9 12.3-5.2 5.2-12.3 8-7.2 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.8-7.1-2.8-16zm20.3 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.6 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.5-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm64.9-0.2q0-8.7 2.7-15.8 2.8-7.2 7.9-12.2 5.1-5.2 12.3-7.9 7.1-2.8 15.8-2.8 8.9 0 16 2.8 7.2 2.7 12.3 7.9 5.2 5 7.9 12.2 2.8 7.1 2.8 15.8 0 8.9-2.8 16-2.7 7.2-7.9 12.3-5.1 5.2-12.3 8-7.1 2.7-16 2.7-8.7 0-15.8-2.7-7.2-2.8-12.3-8-5.1-5.1-7.9-12.3-2.7-7.1-2.7-16zm20.2 0.2q0 9.6 4.7 15.5 4.7 5.8 13.7 5.8 4.7 0 8.1-1.5 3.5-1.6 5.9-4.3 2.4-2.8 3.6-6.7 1.2-4 1.2-8.8 0-4.8-1.2-8.7-1.2-4-3.6-6.8-2.4-2.8-5.9-4.3-3.4-1.6-8.1-1.6-9 0-13.7 5.9-4.7 5.9-4.7 15.5zm65-18l20.3-18.8v56h42v17.5h-62.3z"
                  fill={
                    location.pathname === '/software' ? '#F97316' :
                    location.pathname === '/hardware' ? '#3B82F6' :
                    '#C084FC'
                  }
                  filter="url(#navbar-glow)"
                  animate={{
                    filter: [
                      "url(#navbar-glow)",
                      "url(#navbar-glow-hover)",
                      "url(#navbar-glow)"
                    ]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Pegasus Symbol Path */}
                <motion.path
                  d="m166.7 114q3.7 0 6.7 2.1 47.6 27.2 94.6 55.4 0.8 39.6 0 79.2-30.3 18.6-61.3 36.3-3.8 2.7-7.1-0.4-0.4-37.5-0.8-75.1-16.5-10.3-33.4-20-0.4 79.2-0.8 158.4-1.4 2.1-3.7 2.5-30.6-17.4-60.9-35.4-3.1-1.5-4.6-4.6-0.8-77.5 0-155 1.7-4.6 6.3-3 29.1 17.1 58.3 34.2 2.4 1.6 5 1.7-1.4-36.4-0.4-73 0.8-1.8 2.1-3.3z"
                  fill={
                    location.pathname === '/software' ? '#F97316' :
                    location.pathname === '/hardware' ? '#3B82F6' :
                    '#C084FC'
                  }
                  filter="url(#navbar-glow)"
                  animate={{
                    filter: [
                      "url(#navbar-glow)",
                      "url(#navbar-glow-hover)",
                      "url(#navbar-glow)"
                    ]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                />
              </svg>
            </motion.div>
          </Link>

          {/* Desktop Navigation - Enhanced Centered */}
          <div className="hidden md:flex items-center justify-center flex-1">
            <nav className="flex items-center space-x-2">
              {menuItems.map((item, index) => (
                <motion.a
                  key={item.title}
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection(item.href);
                  }}
                  className={cn(
                    "relative font-medium text-sm transition-all duration-300 py-2.5 px-4 rounded-lg",
                    "font-montserrat tracking-wide",
                    // Default state
                    "text-gray-300 hover:text-white",
                    // Enhanced hover effects
                    "hover:bg-gradient-to-r hover:from-[#C084FC]/10 hover:to-[#9333EA]/10",
                    "hover:shadow-lg hover:shadow-[#C084FC]/20",
                    "hover:backdrop-blur-sm hover:border hover:border-[#C084FC]/30",
                    // Active route styling
                    (location.pathname === item.href ||
                     (location.pathname === '/' && item.href === '/')) &&
                    "text-[#C084FC] bg-gradient-to-r from-[#C084FC]/15 to-[#9333EA]/15 shadow-md shadow-[#C084FC]/25 border border-[#C084FC]/40"
                  )}
                  whileHover={{
                    y: -2,
                    scale: 1.02
                  }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                  }}
                >
                  {item.title}

                  {/* Enhanced underline effect */}
                  <motion.div
                    className="absolute bottom-0 left-1/2 h-0.5 bg-gradient-to-r from-[#C084FC] to-[#9333EA] rounded-full"
                    initial={{ width: 0, x: "-50%" }}
                    whileHover={{ width: "80%" }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                  />
                </motion.a>
              ))}
            </nav>
          </div>

          {/* Authentication Buttons - Enhanced Right Side */}
          <div className="hidden md:flex items-center">
            <AuthButton />
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-3 md:hidden">
            <AuthButton />
            <MobileNav menuItems={menuItems} />
          </div>
        </div>
      </div>

      {/* Subtle bottom glow effect */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-[#C084FC]/30 to-transparent"></div>
    </motion.header>
  );
}

export default Navbar;
