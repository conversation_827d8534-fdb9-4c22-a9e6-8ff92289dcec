import {
  SignedIn,
  SignedOut,
  useAuth,
  useUser,
} from "@clerk/clerk-react";
import { Button } from "@/components/ui/button";
import { User, LogIn } from "lucide-react";
import { Link } from "react-router-dom";

import { UserAvatar } from "./UserAvatar";

export const AuthButton = () => {
  const { isLoaded } = useAuth();
  const { isLoaded: userLoaded } = useUser();

  // Show loading state while auth is loading
  if (!isLoaded || !userLoaded) {
    return (
      <div className="flex items-center gap-2">
        <div className="animate-pulse bg-gray-700 rounded-full h-9 w-9"></div>
      </div>
    );
  }

  return (
    <>
      <SignedOut>
        <div className="flex items-center gap-3">
          <Link to="/sign-in">
            <Button
              variant="ghost"
              size="sm"
              className="text-sm font-medium font-montserrat tracking-wide text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-[#C084FC]/10 hover:to-[#9333EA]/10 hover:shadow-lg hover:shadow-[#C084FC]/20 hover:backdrop-blur-sm hover:border hover:border-[#C084FC]/30 transition-all duration-300 rounded-lg px-4 py-2"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Sign In
            </Button>
          </Link>
          <Link to="/sign-up">
            <Button
              size="sm"
              className="text-sm font-medium font-montserrat tracking-wide bg-gradient-to-r from-[#C084FC] to-[#9333EA] hover:from-[#C084FC]/90 hover:to-[#9333EA]/90 text-white shadow-lg shadow-[#C084FC]/25 hover:shadow-xl hover:shadow-[#C084FC]/30 hover:scale-105 transition-all duration-300 rounded-lg px-4 py-2"
            >
              <User className="w-4 h-4 mr-2" />
              Sign Up
            </Button>
          </Link>
        </div>
      </SignedOut>
      <SignedIn>
        {/* User Avatar with Account Management Menu */}
        <UserAvatar />
      </SignedIn>
    </>
  );
};

export default AuthButton;
