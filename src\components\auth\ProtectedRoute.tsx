import { useAuth } from "@clerk/clerk-react";
import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import LoadingAnimation from "../LoadingAnimation";

interface ProtectedRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

export const ProtectedRoute = ({ 
  children, 
  redirectTo = "/" 
}: ProtectedRouteProps) => {
  const { isLoaded, isSignedIn } = useAuth();

  // Show loading while auth state is being determined
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingAnimation />
      </div>
    );
  }

  // Redirect to specified route if not signed in
  if (!isSignedIn) {
    return <Navigate to={redirectTo} replace />;
  }

  // Render children if user is authenticated
  return <>{children}</>;
};

export default ProtectedRoute;
