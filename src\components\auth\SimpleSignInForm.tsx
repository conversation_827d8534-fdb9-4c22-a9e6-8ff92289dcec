import { useState } from "react";
import { useSignIn } from "@clerk/clerk-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Github } from "lucide-react";
import { toast } from "sonner";
import { Link } from "react-router-dom";

export const SimpleSignInForm = () => {
  const { isLoaded, signIn, setActive } = useSignIn();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);
    setErrors({});

    try {
      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });
        toast.success("Welcome back!");
        window.location.href = "/";
      }
    } catch (err: any) {
      console.error("Sign in error:", err);
      if (err.errors) {
        const newErrors: { [key: string]: string } = {};
        err.errors.forEach((error: any) => {
          if (error.code === "form_identifier_not_found") {
            newErrors.email = "Email not found";
          } else if (error.code === "form_password_incorrect") {
            newErrors.password = "Incorrect password";
          } else {
            newErrors.general = error.message || "An error occurred";
          }
        });
        setErrors(newErrors);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (strategy: "oauth_google" | "oauth_github") => {
    if (!isLoaded) return;

    try {
      await signIn.authenticateWithRedirect({
        strategy,
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/",
      });
    } catch (err) {
      console.error("Social sign in error:", err);
      toast.error("Sign in failed");
    }
  };

  return (
    <div className="w-full max-w-sm mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="space-y-6"
      >
        <div>
          <h1 className="text-2xl font-semibold text-white mb-8">Sign in</h1>
        </div>

        {/* Social Sign In Buttons */}
        <div className="space-y-3">
          <Button
            onClick={() => handleSocialSignIn("oauth_google")}
            variant="outline"
            className="w-full bg-gray-800 border-gray-700 text-white hover:bg-gray-700 transition-colors"
          >
            <Chrome className="w-4 h-4 mr-2" />
            Sign in with Google
          </Button>

          <Button
            onClick={() => handleSocialSignIn("oauth_github")}
            variant="outline"
            className="w-full bg-gray-800 border-gray-700 text-white hover:bg-gray-700 transition-colors"
          >
            <Github className="w-4 h-4 mr-2" />
            Sign in with GitHub
          </Button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-gray-900 px-2 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
              placeholder="Email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
              placeholder="Password"
              required
            />
            {errors.password && (
              <p className="text-red-400 text-sm">{errors.password}</p>
            )}
          </div>

          <Button
            type="submit"
            disabled={isLoading || !email || !password}
            className="w-full bg-white text-black hover:bg-gray-100 transition-colors"
          >
            {isLoading ? "Signing in..." : "Sign in"}
          </Button>
        </form>

        {/* Sign Up Link */}
        <div className="text-center">
          <span className="text-gray-400 text-sm">
            Don't have an account?{" "}
            <Link
              to="/sign-up"
              className="text-white hover:underline font-medium"
            >
              Sign Up
            </Link>
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignInForm;
