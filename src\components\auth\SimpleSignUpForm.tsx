import { useState } from "react";
import { useSignUp } from "@clerk/clerk-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Github } from "lucide-react";
import { toast } from "sonner";
import { Link } from "react-router-dom";

export const SimpleSignUpForm = () => {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [pendingVerification, setPendingVerification] = useState(false);
  const [code, setCode] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);
    setErrors({});

    try {
      await signUp.create({
        emailAddress: email,
        password,
      });

      await signUp.prepareEmailAddressVerification({ strategy: "email_code" });
      setPendingVerification(true);
      toast.success("Verification code sent to your email");
    } catch (err: any) {
      console.error("Sign up error:", err);
      if (err.errors) {
        const newErrors: { [key: string]: string } = {};
        err.errors.forEach((error: any) => {
          if (error.code === "form_identifier_exists") {
            newErrors.email = "Email already exists";
          } else if (error.code === "form_password_pwned") {
            newErrors.password = "Password is too weak";
          } else {
            newErrors.general = error.message || "An error occurred";
          }
        });
        setErrors(newErrors);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);

    try {
      const completeSignUp = await signUp.attemptEmailAddressVerification({
        code,
      });

      if (completeSignUp.status === "complete") {
        await setActive({ session: completeSignUp.createdSessionId });
        toast.success("Account created successfully!");
        window.location.href = "/";
      }
    } catch (err: any) {
      console.error("Verification error:", err);
      setErrors({ code: "Invalid verification code" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignUp = async (strategy: "oauth_google" | "oauth_github") => {
    if (!isLoaded) return;

    try {
      await signUp.authenticateWithRedirect({
        strategy,
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/",
      });
    } catch (err) {
      console.error("Social sign up error:", err);
      toast.error("Sign up failed");
    }
  };

  if (pendingVerification) {
    return (
      <div className="w-full max-w-sm mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          <div>
            <h1 className="text-2xl font-semibold text-white mb-2">Verify your email</h1>
            <p className="text-gray-400 text-sm">
              Enter the verification code sent to {email}
            </p>
          </div>

          <form onSubmit={handleVerification} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="code" className="text-sm text-gray-300">
                Verification Code
              </Label>
              <Input
                id="code"
                type="text"
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
                placeholder="Enter code"
                required
              />
              {errors.code && (
                <p className="text-red-400 text-sm">{errors.code}</p>
              )}
            </div>

            <Button
              type="submit"
              disabled={isLoading || !code}
              className="w-full bg-white text-black hover:bg-gray-100 transition-colors"
            >
              {isLoading ? "Verifying..." : "Verify"}
            </Button>
          </form>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-sm mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="space-y-6"
      >
        <div>
          <h1 className="text-2xl font-semibold text-white mb-8">Create your account</h1>
        </div>

        {/* Social Sign Up Buttons */}
        <div className="space-y-3">
          <Button
            onClick={() => handleSocialSignUp("oauth_google")}
            variant="outline"
            className="w-full bg-gray-800 border-gray-700 text-white hover:bg-gray-700 transition-colors"
          >
            <Chrome className="w-4 h-4 mr-2" />
            Sign up with Google
          </Button>

          <Button
            onClick={() => handleSocialSignUp("oauth_github")}
            variant="outline"
            className="w-full bg-gray-800 border-gray-700 text-white hover:bg-gray-700 transition-colors"
          >
            <Github className="w-4 h-4 mr-2" />
            Sign up with GitHub
          </Button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-gray-900 px-2 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
              placeholder="Email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
              placeholder="Password"
              required
            />
            {errors.password && (
              <p className="text-red-400 text-sm">{errors.password}</p>
            )}
          </div>

          {/* Terms and Privacy */}
          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
            <div className="relative">
              <input
                type="checkbox"
                id="terms"
                className="peer h-4 w-4 rounded border-2 border-gray-600 bg-gray-800 text-pegasus-orange focus:ring-2 focus:ring-pegasus-orange/50 focus:border-pegasus-orange transition-all duration-200 cursor-pointer"
                required
              />
              <div className="absolute inset-0 rounded border-2 border-transparent bg-gradient-to-r from-pegasus-orange/20 to-pegasus-blue/20 opacity-0 peer-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
            </div>
            <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed cursor-pointer">
              I agree to our and read this{" "}
              <Link
                to="/terms-of-service"
                className="text-pegasus-orange hover:text-pegasus-orange/80 font-medium underline decoration-pegasus-orange/50 hover:decoration-pegasus-orange transition-all duration-200"
              >
                Terms of Service
              </Link>
            </label>
          </div>

          <Button
            type="submit"
            disabled={isLoading || !email || !password}
            className="w-full bg-white text-black hover:bg-gray-100 transition-colors"
          >
            {isLoading ? "Creating account..." : "Sign up"}
          </Button>
        </form>

        {/* Sign In Link */}
        <div className="text-center">
          <span className="text-gray-400 text-sm">
            Already have an account?{" "}
            <Link
              to="/sign-in"
              className="text-white hover:underline font-medium"
            >
              Sign in
            </Link>
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignUpForm;
