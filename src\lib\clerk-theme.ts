import { Appearance } from "@clerk/types";

export const clerkTheme: Appearance = {
  baseTheme: "dark", // Force dark theme
  elements: {
    // Main card styling - Dark mode optimized
    card: "shadow-2xl border border-gray-700 bg-gray-900/95 backdrop-blur-sm",

    // Root container
    rootBox: "bg-gray-900",

    // Header styling
    headerTitle: "text-white font-bold text-2xl",
    headerSubtitle: "text-gray-300",

    // Form elements
    formButtonPrimary:
      "bg-gradient-to-r from-[#C084FC] to-[#9333EA] hover:from-[#C084FC]/90 hover:to-[#9333EA]/90 text-white font-medium transition-all duration-200 shadow-lg",
    formButtonSecondary:
      "border border-gray-600 bg-gray-800 text-gray-200 hover:bg-gray-700 transition-colors",

    // Input fields - Dark mode
    formFieldInput:
      "border border-gray-600 bg-gray-800 text-white placeholder:text-gray-400 focus:border-pegasus-orange focus:ring-pegasus-orange/20 focus:bg-gray-750",
    formFieldLabel: "text-gray-200 font-medium",

    // Social buttons - Dark mode
    socialButtonsBlockButton:
      "border border-gray-600 bg-gray-800 text-gray-200 hover:bg-gray-700 transition-colors",
    socialButtonsBlockButtonText: "text-gray-200",

    // Links and navigation
    navbarButton: "text-gray-200 hover:bg-gray-800",
    navbarButtonIcon: "text-gray-400",

    // Dividers and separators
    dividerLine: "bg-gray-700",
    dividerText: "text-gray-400",

    // Error states
    formFieldErrorText: "text-red-400",

    // Loading states
    spinner: "text-[#C084FC]",

    // Modal and overlay - Enhanced dark mode
    modalBackdrop: "bg-black/70 backdrop-blur-sm",
    modalContent: "bg-gray-900 border border-gray-700 shadow-2xl",

    // User button (when signed in) - Dark mode
    userButtonAvatarBox: "border-2 border-pegasus-orange/40 hover:border-pegasus-orange transition-all duration-300 cursor-pointer shadow-lg hover:shadow-pegasus-orange/20",
    userButtonPopoverCard: "bg-gray-900/95 backdrop-blur-sm border border-gray-700 shadow-2xl",
    userButtonPopoverActionButton: "text-gray-200 hover:bg-gray-800/50 transition-colors",
    userButtonPopoverActionButtonText: "text-gray-200",
    userButtonPopoverActionButtonIcon: "text-gray-400",

    // Breadcrumbs - Dark mode
    breadcrumbsItem: "text-gray-400",
    breadcrumbsItemDivider: "text-gray-500",
    breadcrumbsItemCurrent: "text-gray-200",

    // Tabs - Dark mode
    tabButton: "text-gray-400 hover:text-gray-200 data-[state=active]:text-pegasus-orange data-[state=active]:border-pegasus-orange border-b-2 border-transparent",
    tabPanel: "text-gray-200 bg-gray-900",

    // Profile sections - Dark mode
    profileSectionTitle: "text-gray-200 font-semibold",
    profileSectionContent: "text-gray-400",
    profileSection: "bg-gray-800 border border-gray-700",

    // Form field hints - Dark mode
    formFieldHintText: "text-gray-400 text-sm",

    // Success states - Dark mode
    formFieldSuccessText: "text-green-400",

    // Badge and tags - Dark mode
    badge: "bg-pegasus-orange/20 text-pegasus-orange border border-pegasus-orange/30",

    // Menu items - Dark mode
    menuItem: "text-gray-200 hover:bg-gray-800",
    menuItemIcon: "text-gray-400",

    // Alert and notification styling - Dark mode
    alertText: "text-gray-200",
    alert: "bg-gray-800 border border-gray-700 text-gray-200",

    // File upload - Dark mode
    fileDropAreaBox: "border-2 border-dashed border-gray-600 bg-gray-800/50",
    fileDropAreaButtonPrimary: "bg-pegasus-orange hover:bg-pegasus-orange/90 text-white",

    // Organization switcher - Dark mode
    organizationSwitcherTrigger: "border border-gray-600 bg-gray-800 text-gray-200 hover:bg-gray-700",
    organizationSwitcherPopoverCard: "bg-gray-900 border border-gray-700 shadow-2xl",

    // Additional dark mode elements
    userButtonAvatarImage: "rounded-full object-cover",
    userButtonPopoverRootBox: "z-50",
    userButtonPopoverMain: "p-4 space-y-3",
    userButtonPopoverMainIdentifier: "text-gray-200 font-semibold text-base",
    userButtonPopoverMainIdentifierText: "text-gray-400 text-sm mt-1",
    userButtonPopoverFooter: "border-t border-gray-700 pt-3 mt-3",

    // Form container
    formContainer: "bg-gray-900",

    // Additional form elements
    formFieldAction: "text-[#C084FC] hover:text-[#C084FC]/80",
    formHeaderTitle: "text-white",
    formHeaderSubtitle: "text-gray-300",

    // Hide footer elements for cleaner interface
    footer: "hidden",
    footerAction: "hidden",
    footerActionText: "hidden",
    footerPages: "hidden",
    footerPagesLink: "hidden",

    // Enhanced social buttons
    socialButtonsIconButton: "border border-gray-600/50 bg-gray-800/80 hover:bg-gray-700/90 text-gray-200 transition-all duration-300 hover:scale-105 hover:shadow-lg",
    socialButtonsProviderIcon: "w-5 h-5",

    // Enhanced modal styling
    modalCloseButton: "text-gray-400 hover:text-white transition-colors",

    // Enhanced input styling
    formFieldInputShowPasswordButton: "text-gray-400 hover:text-white transition-colors",

    // Enhanced loading states
    formButtonPrimaryLoading: "bg-gradient-to-r from-[#C084FC]/80 to-[#9333EA]/80",

    // Enhanced error styling
    formFieldError: "border-red-500/60 focus:border-red-500 focus:ring-red-500/30",

    // Enhanced success styling
    formFieldSuccess: "border-green-500/60 focus:border-green-500 focus:ring-green-500/30",
  },

  layout: {
    logoImageUrl: undefined, // You can add your logo URL here
    logoLinkUrl: "/",
    showOptionalFields: true,
    socialButtonsPlacement: "bottom",
    socialButtonsVariant: "blockButton",
  },

  variables: {
    // Primary colors - Dark mode optimized
    colorPrimary: "#C084FC", // purple-400
    colorSuccess: "#22c55e", // green-500 for dark mode
    colorWarning: "#f59e0b", // amber-500
    colorDanger: "#ef4444", // red-500
    colorNeutral: "#6b7280", // gray-500

    // Text colors - Dark mode
    colorText: "#f3f4f6", // gray-100
    colorTextSecondary: "#9ca3af", // gray-400
    colorTextOnPrimaryBackground: "#ffffff",

    // Background colors - Dark mode
    colorBackground: "#111827", // gray-900
    colorInputBackground: "#1f2937", // gray-800
    colorInputText: "#f3f4f6", // gray-100

    // Border and shadow
    borderRadius: "0.5rem",
    colorShimmer: "#374151", // gray-700

    // Typography
    fontFamily: "inherit",
    fontSize: "0.875rem",
    fontWeight: {
      normal: "400",
      medium: "500",
      bold: "700",
    },

    // Spacing
    spacingUnit: "1rem",
  },
};
