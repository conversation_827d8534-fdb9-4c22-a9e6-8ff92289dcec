/**
 * Clerk UI Customization Utilities
 * This file contains functions to hide Clerk footer elements and improve text styling
 */

// Function to hide Clerk footer elements
export const hideClerkFooter = () => {
  // Wait for DOM to be ready
  const hideElements = () => {
    // Selectors for Clerk footer elements
    const footerSelectors = [
      '.cl-footer',
      '.cl-footerPages',
      '.cl-footerPagesLink',
      '.cl-footerAction',
      '.cl-footerActionText',
      '.cl-footerActionLink',
      '.cl-userButtonPopoverFooter',
      '.cl-organizationSwitcherPopoverFooter',
      '.cl-poweredBy',
      '.cl-poweredByText',
      '.cl-poweredByLink',
      '.cl-developmentModeWarning',
      '[data-localization-key="footerActionLink__signIn"]',
      '[data-localization-key="footerActionLink__signUp"]',
      '[data-localization-key="footerActionLink__useAnotherMethod"]',
      '[data-localization-key="footerPageLink__help"]',
      '[data-localization-key="footerPageLink__privacy"]',
      '[data-localization-key="footerPageLink__terms"]',
      '[data-localization-key="footerText__poweredBy"]',
      // Internal Clerk classes that might contain "Secured by"
      '[class*="cl-internal"]',
      // Any element containing specific text
      '*:contains("Secured by")',
      '*:contains("Development mode")',
      '*:contains("Help")',
      '*:contains("Privacy")',
      '*:contains("Terms")'
    ];

    footerSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (element) {
            (element as HTMLElement).style.display = 'none';
            (element as HTMLElement).style.visibility = 'hidden';
            (element as HTMLElement).style.opacity = '0';
            (element as HTMLElement).style.height = '0';
            (element as HTMLElement).style.overflow = 'hidden';
          }
        });
      } catch (error) {
        // Ignore errors for invalid selectors
      }
    });

    // Hide elements by text content
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      const textContent = element.textContent?.toLowerCase() || '';
      if (
        textContent.includes('secured by') ||
        textContent.includes('development mode') ||
        (textContent.includes('help') && element.closest('.cl-footer')) ||
        (textContent.includes('privacy') && element.closest('.cl-footer')) ||
        (textContent.includes('terms') && element.closest('.cl-footer'))
      ) {
        (element as HTMLElement).style.display = 'none';
      }
    });
  };

  // Run immediately
  hideElements();

  // Run after a short delay to catch dynamically loaded content
  setTimeout(hideElements, 100);
  setTimeout(hideElements, 500);
  setTimeout(hideElements, 1000);

  // Set up a MutationObserver to watch for new elements
  const observer = new MutationObserver((mutations) => {
    let shouldHide = false;
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldHide = true;
      }
    });
    if (shouldHide) {
      setTimeout(hideElements, 50);
    }
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  return observer;
};

// Function to enhance Clerk text styling
export const enhanceClerkStyling = () => {
  const enhanceElements = () => {
    // Enhance form titles
    const titles = document.querySelectorAll('.cl-formHeaderTitle, .cl-headerTitle');
    titles.forEach(title => {
      (title as HTMLElement).style.fontSize = '1.5rem';
      (title as HTMLElement).style.fontWeight = '700';
      (title as HTMLElement).style.color = '#ffffff';
      (title as HTMLElement).style.marginBottom = '1rem';
      (title as HTMLElement).style.letterSpacing = '-0.025em';
    });

    // Enhance form subtitles
    const subtitles = document.querySelectorAll('.cl-formHeaderSubtitle, .cl-headerSubtitle');
    subtitles.forEach(subtitle => {
      (subtitle as HTMLElement).style.color = '#d1d5db';
      (subtitle as HTMLElement).style.fontSize = '1rem';
      (subtitle as HTMLElement).style.marginBottom = '1.5rem';
      (subtitle as HTMLElement).style.lineHeight = '1.6';
    });

    // Enhance form labels
    const labels = document.querySelectorAll('.cl-formFieldLabel');
    labels.forEach(label => {
      (label as HTMLElement).style.color = '#e5e7eb';
      (label as HTMLElement).style.fontWeight = '500';
      (label as HTMLElement).style.fontSize = '0.875rem';
      (label as HTMLElement).style.marginBottom = '0.5rem';
    });

    // Enhance input fields
    const inputs = document.querySelectorAll('.cl-formFieldInput');
    inputs.forEach(input => {
      (input as HTMLElement).style.backgroundColor = 'rgba(31, 41, 55, 0.6)';
      (input as HTMLElement).style.borderColor = 'rgba(75, 85, 99, 0.4)';
      (input as HTMLElement).style.color = '#ffffff';
      (input as HTMLElement).style.borderRadius = '0.5rem';
      (input as HTMLElement).style.padding = '0.75rem 1rem';
      (input as HTMLElement).style.fontSize = '0.875rem';
      (input as HTMLElement).style.transition = 'all 0.2s ease';
    });

    // Enhance buttons
    const buttons = document.querySelectorAll('.cl-formButtonPrimary');
    buttons.forEach(button => {
      (button as HTMLElement).style.background = 'linear-gradient(to right, #f97316, #3b82f6)';
      (button as HTMLElement).style.color = '#ffffff';
      (button as HTMLElement).style.fontWeight = '500';
      (button as HTMLElement).style.padding = '0.75rem 1.5rem';
      (button as HTMLElement).style.borderRadius = '0.5rem';
      (button as HTMLElement).style.transition = 'all 0.2s ease';
      (button as HTMLElement).style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
    });

    // Enhance social buttons
    const socialButtons = document.querySelectorAll('.cl-socialButtonsBlockButton');
    socialButtons.forEach(button => {
      (button as HTMLElement).style.backgroundColor = 'rgba(31, 41, 55, 0.6)';
      (button as HTMLElement).style.borderColor = 'rgba(75, 85, 99, 0.4)';
      (button as HTMLElement).style.color = '#e5e7eb';
      (button as HTMLElement).style.borderRadius = '0.5rem';
      (button as HTMLElement).style.padding = '0.75rem 1rem';
      (button as HTMLElement).style.fontWeight = '500';
      (button as HTMLElement).style.transition = 'all 0.2s ease';
    });

    // Enhance divider text
    const dividers = document.querySelectorAll('.cl-dividerText');
    dividers.forEach(divider => {
      (divider as HTMLElement).style.color = '#9ca3af';
      (divider as HTMLElement).style.fontSize = '0.875rem';
      (divider as HTMLElement).style.fontWeight = '500';
    });

    // Enhance error text
    const errors = document.querySelectorAll('.cl-formFieldErrorText');
    errors.forEach(error => {
      (error as HTMLElement).style.color = '#f87171';
      (error as HTMLElement).style.fontSize = '0.75rem';
      (error as HTMLElement).style.marginTop = '0.25rem';
    });

    // Enhance success text
    const success = document.querySelectorAll('.cl-formFieldSuccessText');
    success.forEach(successEl => {
      (successEl as HTMLElement).style.color = '#34d399';
      (successEl as HTMLElement).style.fontSize = '0.75rem';
      (successEl as HTMLElement).style.marginTop = '0.25rem';
    });

    // Enhance hint text
    const hints = document.querySelectorAll('.cl-formFieldHintText');
    hints.forEach(hint => {
      (hint as HTMLElement).style.color = '#9ca3af';
      (hint as HTMLElement).style.fontSize = '0.75rem';
      (hint as HTMLElement).style.marginTop = '0.25rem';
    });
  };

  // Run immediately
  enhanceElements();

  // Run after delays to catch dynamically loaded content
  setTimeout(enhanceElements, 100);
  setTimeout(enhanceElements, 500);
  setTimeout(enhanceElements, 1000);

  // Set up a MutationObserver to watch for new elements
  const observer = new MutationObserver((mutations) => {
    let shouldEnhance = false;
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldEnhance = true;
      }
    });
    if (shouldEnhance) {
      setTimeout(enhanceElements, 50);
    }
  });

  // Start observing
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  return observer;
};

// Initialize all customizations
export const initializeClerkCustomizations = () => {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      hideClerkFooter();
      enhanceClerkStyling();
    });
  } else {
    hideClerkFooter();
    enhanceClerkStyling();
  }
};

// Auto-initialize when this module is imported
if (typeof window !== 'undefined') {
  initializeClerkCustomizations();
}
