import { UserProfile } from "@clerk/clerk-react";
import ProtectedRoute from "./ProtectedRoute";
import { clerkTheme } from "@/lib/clerk-theme";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Settings, Shield, User, Bell, Key, Palette, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export const UserProfilePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-20, 20, -20],
      rotate: [0, 10, -10, 0],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const quickActions = [
    { icon: User, title: "Profile", desc: "Personal Info", color: "text-pegasus-orange" },
    { icon: Shield, title: "Security", desc: "Password & 2FA", color: "text-green-400" },
    { icon: Bell, title: "Notifications", desc: "Email & SMS", color: "text-pegasus-blue" },
    { icon: Key, title: "API Keys", desc: "Developer Access", color: "text-purple-400" }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen relative overflow-hidden">
        {/* Advanced Background with Multiple Layers */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-950 via-gray-900 to-slate-900"></div>

        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')] opacity-10 animate-pulse"></div>

        {/* Dynamic Gradient Orbs */}
        <motion.div
          className="absolute top-1/4 left-1/3 w-96 h-96 bg-gradient-to-r from-pegasus-orange/15 to-orange-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-gradient-to-r from-pegasus-blue/15 to-blue-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
        />

        {/* Floating Geometric Shapes */}
        <motion.div
          className="absolute top-20 right-20 w-32 h-32 border border-pegasus-orange/20 rounded-2xl"
          variants={floatingVariants}
          animate="animate"
        />

        <motion.div
          className="absolute bottom-20 left-20 w-24 h-24 border border-pegasus-blue/20 rounded-full"
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: "4s" }}
        />

        <motion.div
          className="absolute top-1/2 right-10 w-8 h-8 bg-pegasus-orange/40 rounded-full"
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <AnimatePresence>
          {isLoaded && (
            <motion.div
              className="container mx-auto py-12 px-6 relative z-10"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Navigation Header */}
              <motion.div
                className="mb-8"
                variants={itemVariants}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white hover:bg-white/10 group mb-4"
                  onClick={() => window.history.back()}
                >
                  <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                  Back to Dashboard
                </Button>
              </motion.div>

              {/* Enhanced Header Section */}
              <motion.div
                className="text-center mb-12"
                variants={itemVariants}
              >
                <motion.div
                  className="inline-flex items-center justify-center w-24 h-24 rounded-3xl bg-gradient-to-br from-pegasus-orange/20 to-pegasus-blue/20 border border-white/20 mb-8"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Settings className="w-12 h-12 text-pegasus-orange" />
                </motion.div>

                <h1 className="text-6xl font-bold bg-gradient-to-r from-white via-pegasus-orange to-pegasus-blue bg-clip-text text-transparent mb-6">
                  Account Center
                </h1>

                <p className="text-gray-300/90 text-xl font-medium max-w-3xl mx-auto mb-8">
                  Manage your account settings, security preferences, and personal information with advanced controls
                </p>

                <div className="flex items-center justify-center gap-2 text-sm text-gray-400 mb-8">
                  <Palette className="w-4 h-4 text-pegasus-orange" />
                  <span>Customizable Interface</span>
                  <Shield className="w-4 h-4 text-green-400 ml-4" />
                  <span>Enterprise Security</span>
                </div>
              </motion.div>

              {/* Quick Actions Grid */}
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
                variants={itemVariants}
              >
                {quickActions.map((action, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card className="glass-card backdrop-blur-2xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all duration-300 cursor-pointer group">
                      <CardContent className="p-6 text-center">
                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-white/10 to-white/5 border border-white/20 mb-4 group-hover:scale-110 transition-transform duration-300`}>
                          <action.icon className={`w-6 h-6 ${action.color}`} />
                        </div>
                        <h3 className="text-white font-semibold mb-1">{action.title}</h3>
                        <p className="text-gray-400 text-sm">{action.desc}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>

              {/* Enhanced Clerk Profile Container */}
              <motion.div
                className="flex justify-center"
                variants={itemVariants}
              >
                <motion.div
                  className="w-full max-w-5xl glass-card backdrop-blur-2xl bg-white/5 border border-white/10 overflow-hidden"
                  whileHover={{ y: -3 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="p-4">
                    <UserProfile
                      appearance={clerkTheme}
                      routing="path"
                      path="/profile"
                    />
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ProtectedRoute>
  );
};

export default UserProfilePage;
