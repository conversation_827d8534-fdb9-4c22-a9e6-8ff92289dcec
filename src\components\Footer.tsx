import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { FacebookIcon, TwitterIcon, GithubIcon } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-12 w-full mt-auto">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4 text-[#C084FC]">Pegasus Tools</h3>
            <p className="text-gray-400 mb-4">
              The ultimate smartphone flashing and unlocking solution for repair professionals.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" className="text-gray-400 hover:text-[#C084FC] transition-colors">
                <span className="sr-only">Facebook</span>
                <FacebookIcon className="h-6 w-6" />
              </a>
              <a href="https://twitter.com" className="text-gray-400 hover:text-[#C084FC] transition-colors">
                <span className="sr-only">Twitter</span>
                <TwitterIcon className="h-6 w-6" />
              </a>
              <a href="https://github.com" className="text-gray-400 hover:text-[#C084FC] transition-colors">
                <span className="sr-only">GitHub</span>
                <GithubIcon className="h-6 w-6" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Product</h3>
            <ul className="space-y-2">
              <li><Link to="/" className="text-gray-400 hover:text-[#C084FC] transition-colors">Home</Link></li>
              <li><Link to="/software" className="text-gray-400 hover:text-[#C084FC] transition-colors">Software</Link></li>
              <li><Link to="/hardware" className="text-gray-400 hover:text-[#C084FC] transition-colors">Hardware</Link></li>
              <li><Link to="/whats-new" className="text-gray-400 hover:text-[#C084FC] transition-colors">What's New</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Sales</h3>
            <ul className="space-y-2">
              <li><Link to="/resellers" className="text-gray-400 hover:text-[#C084FC] transition-colors">Resellers</Link></li>
              <li><Link to="/payment-methods" className="text-gray-400 hover:text-[#C084FC] transition-colors">Payment Methods</Link></li>
              <li><Link to="/help-center" className="text-gray-400 hover:text-[#C084FC] transition-colors">Sales Center</Link></li>
            </ul>
          </div>

         <div>
            <h3 className="text-xl font-bold mb-4">Company</h3>
            <ul className="space-y-2">
              <li><Link to="/knowledge-base" className="text-gray-400 hover:text-[#C084FC] transition-colors">Knowledge Base</Link></li>
              <li><Link to="/faq" className="text-gray-400 hover:text-[#C084FC] transition-colors">FAQ</Link></li>
              <li><Link to="/terms-of-service" className="text-gray-400 hover:text-[#C084FC] transition-colors">Terms of Service</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">Support</h3>
            <ul className="space-y-2">
              <li><Link to="/help-center" className="text-gray-400 hover:text-[#C084FC] transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="text-gray-400 hover:text-[#C084FC] transition-colors">Contact</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
          <p>© {new Date().getFullYear()} Pegasus Tool. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
