# Clerk Authentication Setup Guide

This guide will help you complete the Clerk authentication integration for your React + Vite project.

## 🚀 Quick Setup

### 1. Get Your Clerk Publishable Key

1. Go to [Clerk Dashboard](https://dashboard.clerk.com/)
2. Create a new application or select an existing one
3. Navigate to **API Keys** in the sidebar
4. Copy your **Publishable Key** (starts with `pk_test_` or `pk_live_`)

### 2. Configure Environment Variables

1. Open the `.env.local` file in your project root
2. Replace `pk_test_your_clerk_publishable_key_here` with your actual Clerk publishable key:

```env
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_key_here
```

### 3. Configure Clerk Dashboard

In your Clerk Dashboard, configure the following:

#### Allowed Redirect URLs
Add these URLs to your allowed redirect URLs:
- `http://localhost:5173` (for development)
- `http://localhost:5173/` (for development)
- Your production domain (e.g., `https://yourdomain.com`)

#### Sign-in/Sign-up URLs
- Sign-in URL: `/sign-in`
- Sign-up URL: `/sign-up`
- After sign-in URL: `/`
- After sign-up URL: `/`

## 🎨 Features Included

### ✅ Authentication Components
- **AuthButton**: Smart button that shows sign-in/sign-up for guests and user menu for authenticated users
- **SignInPage**: Full-page sign-in component with custom styling
- **SignUpPage**: Full-page sign-up component with custom styling
- **UserProfilePage**: Protected user profile management page
- **ProtectedRoute**: Wrapper component for protecting routes

### ✅ Integration Features
- **Custom Theme**: Matches your existing design system with Pegasus orange and green colors
- **Navbar Integration**: Authentication buttons integrated into your existing navbar
- **Mobile Support**: Responsive design for mobile devices
- **Supabase Sync**: Automatic user data synchronization with Supabase (optional)
- **TypeScript Support**: Full TypeScript support with proper types

### ✅ Available Routes
- `/sign-in` - Sign in page
- `/sign-up` - Sign up page  
- `/profile` - User profile page (protected)

## 🔧 Usage Examples

### Basic Authentication Check
```tsx
import { useAuth } from "@clerk/clerk-react";

function MyComponent() {
  const { isSignedIn, user } = useAuth();
  
  if (isSignedIn) {
    return <div>Hello {user.firstName}!</div>;
  }
  
  return <div>Please sign in</div>;
}
```

### Protecting Routes
```tsx
import { ProtectedRoute } from "@/components/auth";

function App() {
  return (
    <Routes>
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <Dashboard />
        </ProtectedRoute>
      } />
    </Routes>
  );
}
```

### Custom User Data Access
```tsx
import { useUser } from "@clerk/clerk-react";

function UserInfo() {
  const { user } = useUser();
  
  return (
    <div>
      <img src={user?.imageUrl} alt="Profile" />
      <p>{user?.emailAddresses[0]?.emailAddress}</p>
    </div>
  );
}
```

## 🎯 Next Steps

1. **Test the Integration**: Start your development server and test the authentication flow
2. **Customize Styling**: Modify the theme in `src/lib/clerk-theme.ts` if needed
3. **Add Social Providers**: Configure Google, GitHub, etc. in Clerk Dashboard
4. **Set Up Webhooks**: Configure webhooks for user events (optional)
5. **Production Setup**: Update environment variables for production deployment

## 🛠️ Troubleshooting

### Common Issues

**"Missing Publishable Key" Error**
- Make sure your `.env.local` file has the correct `VITE_CLERK_PUBLISHABLE_KEY`
- Restart your development server after adding environment variables

**Redirect Issues**
- Check that your redirect URLs are properly configured in Clerk Dashboard
- Ensure URLs match exactly (including trailing slashes)

**Styling Issues**
- The custom theme is in `src/lib/clerk-theme.ts`
- Modify the theme variables to match your design system

## 📚 Additional Resources

- [Clerk Documentation](https://clerk.com/docs)
- [Clerk React SDK](https://clerk.com/docs/references/react/overview)
- [Clerk Customization](https://clerk.com/docs/customization/overview)

## 🔐 Security Notes

- Never commit your actual API keys to version control
- Use environment variables for all sensitive configuration
- The publishable key is safe to use in client-side code
- Keep your secret key (if used) server-side only
